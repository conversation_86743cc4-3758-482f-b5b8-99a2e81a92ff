---
title: Security Framework - Revised
type: note
permalink: revision-swarm/security/security-framework-revised
tags:
- '#security'
- '#revision'
- '#foundational'
- '#agent-focused'
---

# Security Framework - Foundational Patterns

## Framework Authority
This document implements specifications from the canonical tech-framework.md located at /Users/<USER>/<PERSON>-<PERSON>/<PERSON>-<PERSON>/tech-framework.md

As stated in the canonical framework: "Agents: use this framework as the canonical source."

## Purpose
Foundational security patterns for agent implementation focusing on basic authentication, authorization, TLS setup, and secrets management. This document provides pseudocode patterns and configurations for learning and implementation by agents.

## Core Security Components

### 1. Basic Authentication Pattern

**Pseudocode Pattern:**
```pseudocode
// Basic JWT Authentication Flow
function authenticate_request(request):
    token = extract_bearer_token(request.headers)
    if not token:
        return error(401, "No authentication provided")
    
    claims = verify_jwt_token(token, public_key)
    if not claims or claims.expired:
        return error(401, "Invalid or expired token")
    
    request.context.user_id = claims.subject
    return success()

// Token Generation Pattern
function generate_auth_token(user_id, expires_in):
    claims = {
        subject: user_id,
        issued_at: current_timestamp(),
        expires_at: current_timestamp() + expires_in
    }
    return sign_jwt(claims, private_key)
```

**Configuration Pattern:**
```yaml
authentication:
  jwt:
    algorithm: RS256
    public_key_path: /path/to/public.pem
    private_key_path: /path/to/private.pem
    token_expiry: 3600  # seconds
```

### 2. Simple Authorization Pattern

**Pseudocode Pattern:**
```pseudocode
// Role-Based Access Control Pattern
function check_permission(user_id, resource, action):
    user_roles = get_user_roles(user_id)
    
    for role in user_roles:
        permissions = get_role_permissions(role)
        if has_permission(permissions, resource, action):
            return allow()
    
    return deny()

// Permission Definition Structure
permissions = {
    "reader": ["read:*"],
    "writer": ["read:*", "write:own"],
    "admin": ["read:*", "write:*", "delete:*"]
}
```

**Configuration Pattern:**
```yaml
authorization:
  type: role_based
  default_role: reader
  roles:
    - name: reader
      permissions: [read]
    - name: writer  
      permissions: [read, write]
    - name: admin
      permissions: [read, write, delete, admin]
```

### 3. TLS Configuration Pattern

**Pseudocode Pattern:**
```pseudocode
// TLS Server Setup
function create_tls_server(cert_path, key_path):
    tls_config = {
        certificate: load_certificate(cert_path),
        private_key: load_private_key(key_path),
        min_version: "TLS_1_2",
        cipher_suites: ["TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"]
    }
    
    return create_server_with_tls(tls_config)

// TLS Client Configuration
function create_tls_client(ca_cert_path):
    tls_config = {
        ca_certificate: load_certificate(ca_cert_path),
        verify_hostname: true,
        min_version: "TLS_1_2"
    }
    
    return create_client_with_tls(tls_config)
```

**Configuration Pattern:**
```yaml
tls:
  server:
    cert_path: /certs/server.crt
    key_path: /certs/server.key
    min_version: TLS1.2
  client:
    ca_cert_path: /certs/ca.crt
    verify_hostname: true
```

### 4. Basic Secrets Management

**Pseudocode Pattern:**
```pseudocode
// Environment-Based Secrets
function load_secrets():
    secrets = {
        database_url: get_env("DATABASE_URL"),
        api_key: get_env("API_KEY"),
        jwt_secret: get_env("JWT_SECRET")
    }
    
    // Validate required secrets
    for key, value in secrets:
        if not value:
            error("Missing required secret: " + key)
    
    return secrets

// File-Based Secrets Pattern
function load_secrets_from_file(path):
    if not file_exists(path):
        error("Secrets file not found")
    
    // Ensure proper file permissions
    if not check_file_permissions(path, "600"):
        error("Insecure secrets file permissions")
    
    return parse_secrets_file(path)
```

**Configuration Pattern:**
```yaml
secrets:
  source: environment  # or 'file'
  file_path: /secrets/app.secrets
  required:
    - DATABASE_URL
    - JWT_SECRET
    - API_KEY
```

### 5. Basic Security Middleware

**Pseudocode Pattern:**
```pseudocode
// Security Headers Middleware
function security_headers_middleware(request, response, next):
    response.headers.add("X-Content-Type-Options", "nosniff")
    response.headers.add("X-Frame-Options", "DENY")
    response.headers.add("X-XSS-Protection", "1; mode=block")
    response.headers.add("Strict-Transport-Security", "max-age=31536000")
    
    return next(request, response)

// Rate Limiting Pattern
function rate_limit_middleware(request, response, next):
    client_id = get_client_identifier(request)
    
    if exceeded_rate_limit(client_id):
        return error(429, "Rate limit exceeded")
    
    increment_request_count(client_id)
    return next(request, response)
```

### 6. Basic Audit Logging

**Pseudocode Pattern:**
```pseudocode
// Security Event Logging
function log_security_event(event_type, details):
    event = {
        timestamp: current_timestamp(),
        event_type: event_type,
        details: details,
        source_ip: get_request_ip(),
        user_id: get_current_user_id()
    }
    
    append_to_audit_log(event)

// Common Security Events to Log
security_events = [
    "authentication_success",
    "authentication_failure", 
    "authorization_denied",
    "invalid_token",
    "rate_limit_exceeded",
    "suspicious_activity"
]
```

### 7. NATS Security Patterns

**Pseudocode Pattern - mTLS Configuration:**
```pseudocode
// Initialize secure NATS connection with mTLS
function init_secure_nats(tenant_id):
    // Load certificates from secure storage
    ca_cert = load_file("/secrets/ca.crt")
    client_cert = load_file("/secrets/client.crt")
    client_key = load_file("/secrets/client.key")
    
    // Configure connection with tenant-specific identity
    options = {
        require_tls: true,
        ca_certificate: ca_cert,
        client_certificate: client_cert,
        client_key: client_key,
        client_name: "tenant_" + tenant_id + "_agent"
    }
    
    return connect_nats(NATS_URL, options)

// Apply rate limiting and backpressure
function configure_nats_limits(connection):
    connection.subscription_capacity = 1000  // Bounded buffer
    connection.ping_interval = 10  // seconds
    connection.reconnect_buffer_size = 8388608  // 8MB
```

**Configuration Pattern - Server mTLS:**
```hocon
# NATS server mTLS configuration
tls {
  cert_file: "./certs/nats-server.crt"
  key_file:  "./certs/nats-server.key"
  ca_file:   "./certs/ca.crt"
  verify: true  # Enforce client certificates
}

cluster {
  tls {
    cert_file: "./certs/cluster.crt"
    key_file:  "./certs/cluster.key"
    ca_file:   "./certs/ca.crt"
  }
}
```

**Account-Based Tenant Isolation Pattern:**
```yaml
# NATS account isolation pattern
account_isolation:
  principle: "Each tenant gets isolated NATS account"
  benefits:
    - Complete namespace separation
    - No subject prefix complexity
    - Built-in multi-tenancy support
  implementation: |
    nsc add account --name tenantA
    nsc edit account --name tenantA \
      --js-mem-storage 512M \
      --js-disk-storage 1G \
      --js-streams 10 \
      --js-consumer 50
```

**Fine-Grained ACL Configuration:**
```json
// Per-user permission model
{
  "users": [
    {
      "user": "admin",
      "permissions": {
        "publish": [ ">" ],     // Full access
        "subscribe": [ ">" ]
      }
    },
    {
      "user": "tenantA_bot",
      "permissions": {
        "publish": { "allow": ["tenantA.>"] },
        "subscribe": { "allow": ["tenantA.>"] }
      }
    }
  ]
}
```

**Resource Quota Enforcement:**
```yaml
# Per-account resource limits
jetstream_limits:
  per_account:
    max_memory: 512M
    max_disk: 1G
    max_streams: 10
    max_consumers: 100
  per_stream:
    max_bytes: configurable
    max_msgs: configurable
    max_age: configurable
    discard_policy: old_on_full
  connection_limits:
    max_connections: 100
    max_subscriptions: 1000
    max_payload_size: 1MB
```

**Key Rotation Pattern:**
```pseudocode
// Zero-downtime key rotation state machine
key_rotation_states = [
    "KEY_A_ACTIVE",
    "STAGING_NEW_KEY",
    "RELOADING_CONFIG",
    "KEY_B_ACTIVE"
]

// Signal handler for hot reload
function handle_sighup_signal():
    if signal_received == SIGHUP:
        // Atomically swap API keys in memory
        rotate_keys()
        reload_tls_certificates()
        update_active_connections()
```

**Critical NATS Security Patterns:**
1. **Never share accounts between tenants** - Use NATS accounts for true isolation
2. **Always enforce mTLS** - Both client and cluster connections must verify certificates
3. **Apply least privilege** - Restrict subjects to minimum required patterns
4. **Set resource quotas** - Prevent any tenant from exhausting cluster resources
5. **Rotate secrets regularly** - Use SIGHUP for zero-downtime key rotation
6. **Monitor wildcard usage** - Detect and prevent unauthorized subject access

## Implementation Guidelines

### Authentication Flow
1. Extract authentication token from request
2. Verify token signature and expiration
3. Extract user identity from token claims
4. Attach identity to request context

### Authorization Flow
1. Identify resource and action from request
2. Retrieve user roles/permissions
3. Check if user has required permission
4. Allow or deny based on permission check

### TLS Setup Flow
1. Generate or obtain TLS certificates
2. Configure minimum TLS version (1.2+)
3. Select secure cipher suites
4. Enable hostname verification for clients

### Secrets Management Flow
1. Define required secrets
2. Load from environment or secure file
3. Validate all required secrets present
4. Use secrets for service configuration

### NATS Security Flow
1. Generate or obtain mTLS certificates for NATS
2. Create isolated accounts for each tenant
3. Configure ACLs for subject-based access control
4. Set resource quotas to prevent resource exhaustion
5. Implement key rotation handlers for hot reload

## Security Checklist for Agents

- [ ] Implement authentication before processing requests
- [ ] Check authorization for protected resources
- [ ] Enable TLS for all network communication
- [ ] Store secrets securely (environment variables or encrypted files)
- [ ] Add security headers to HTTP responses
- [ ] Implement rate limiting for API endpoints
- [ ] Log security-relevant events for audit trails
- [ ] Validate and sanitize all input data
- [ ] Use secure random number generation for tokens
- [ ] Set appropriate timeouts for authentication tokens
- [ ] Configure NATS with mTLS for secure messaging
- [ ] Implement account-based isolation for multi-tenant NATS
- [ ] Set resource quotas for NATS accounts and streams
- [ ] Configure fine-grained ACLs for NATS subjects
- [ ] Implement key rotation for zero-downtime secret updates

## Configuration Templates

### Basic Security Configuration
```yaml
security:
  authentication:
    enabled: true
    type: jwt
    token_expiry: 3600
  
  authorization:
    enabled: true
    type: role_based
    default_role: reader
  
  tls:
    enabled: true
    min_version: TLS1.2
  
  rate_limiting:
    enabled: true
    requests_per_minute: 60
  
  audit_logging:
    enabled: true
    log_path: /logs/security.log
  
  nats:
    enabled: true
    mtls:
      cert_path: /certs/nats-client.crt
      key_path: /certs/nats-client.key
      ca_path: /certs/ca.crt
    account_isolation: true
    resource_quotas:
      max_memory: 512M
      max_disk: 1G
      max_connections: 100
```

## 11. Hook Execution Sandbox Pattern

### 11.1 Non-Root User Execution Pattern

**Pseudocode Pattern:**
```pseudocode
// Hook execution with privilege isolation
function execute_hook_safely(hook_script, payload):
    // Ensure hook runs under non-root user
    execution_user = get_non_privileged_user()  // e.g., "claude-hook-runner"

    // Create isolated execution environment
    sandbox_config = {
        user: execution_user,
        working_directory: "/tmp/hook-sandbox",
        environment_variables: filter_safe_env_vars(),
        resource_limits: {
            max_memory: "128M",
            max_cpu_time: "30s",
            max_file_descriptors: 64,
            max_processes: 1
        },
        filesystem_access: {
            read_only: ["/usr", "/lib", "/bin"],
            read_write: ["/tmp/hook-sandbox"],
            no_access: ["/etc", "/root", "/home"]
        }
    }

    // Execute with timeout and resource constraints
    result = execute_with_sandbox(hook_script, payload, sandbox_config)

    // Clean up sandbox environment
    cleanup_sandbox_directory()

    return result

// User privilege management
function setup_hook_user():
    // Create dedicated user for hook execution
    create_user("claude-hook-runner", {
        home_directory: "/var/lib/claude-hooks",
        shell: "/bin/bash",
        groups: ["claude-hooks"],
        no_login: false,
        system_user: true
    })

    // Set up hook directory permissions
    set_directory_permissions("/var/lib/claude-hooks", {
        owner: "claude-hook-runner",
        group: "claude-hooks",
        permissions: "750"
    })
```

**Configuration Pattern:**
```yaml
hook_security:
  execution_user: claude-hook-runner
  sandbox_directory: /tmp/hook-sandbox
  resource_limits:
    max_memory_mb: 128
    max_cpu_seconds: 30
    max_file_descriptors: 64
    max_processes: 1

  filesystem_isolation:
    read_only_paths:
      - /usr
      - /lib
      - /bin
      - /etc/passwd
    read_write_paths:
      - /tmp/hook-sandbox
    blocked_paths:
      - /etc
      - /root
      - /home
      - /var/lib/claude-hooks/.ssh

  network_isolation:
    allow_outbound: false
    allow_localhost: true
    blocked_ports: [22, 3389, 5432, 27017]
```

### 11.2 Hook Script Validation Pattern

**Pseudocode Pattern:**
```pseudocode
// Validate hook scripts before execution
function validate_hook_script(script_path):
    // Check file permissions
    file_info = get_file_info(script_path)
    if file_info.owner != "claude-hook-runner":
        return error("Hook script must be owned by claude-hook-runner")

    if file_info.permissions & WORLD_WRITABLE:
        return error("Hook script cannot be world-writable")

    // Validate script content
    script_content = read_file(script_path)

    // Check for dangerous patterns
    dangerous_patterns = [
        "sudo", "su -", "chmod 777", "rm -rf /",
        "curl.*|.*sh", "wget.*|.*sh", "eval",
        "/etc/passwd", "/etc/shadow"
    ]

    for pattern in dangerous_patterns:
        if matches_pattern(script_content, pattern):
            return error("Hook script contains dangerous pattern: " + pattern)

    // Validate shebang
    if not script_content.starts_with("#!/"):
        return error("Hook script must have valid shebang")

    return success()

// Hook directory security
function secure_hook_directory(hook_dir):
    // Ensure proper ownership and permissions
    set_ownership(hook_dir, "claude-hook-runner", "claude-hooks")
    set_permissions(hook_dir, "750")  // rwxr-x---

    // Validate all hook scripts
    for script in list_files(hook_dir):
        validate_hook_script(script)
        set_permissions(script, "750")  // rwxr-x---
```

## Pattern Implementation Notes

- All patterns are foundational and can be extended as needed
- Focus on understanding core security concepts before adding complexity
- Use standard libraries for cryptographic operations
- Test security configurations in isolated environments
- Follow the principle of least privilege for all access control

# CONCRETE IMPLEMENTATIONS

This section provides production-ready implementations that leave zero security implementation decisions for consumers. All security parameters, algorithms, and procedures are specified concretely.

## 1. Certificate Management Implementation

### 1.1 Certificate Generation Scripts

**Complete Certificate Authority Setup:**
```bash
#!/bin/bash
# generate_ca.sh - Complete CA setup for Mister Smith Framework

set -euo pipefail

CA_DIR="/etc/mister-smith/certs"
CA_KEY_SIZE=4096
CERT_VALIDITY_DAYS=3650
SERVER_CERT_VALIDITY_DAYS=90
CLIENT_CERT_VALIDITY_DAYS=365

# Create directory structure
mkdir -p $CA_DIR/{ca,server,client,crl}
cd $CA_DIR

# Generate CA private key (RSA 4096-bit)
openssl genrsa -out ca/ca-key.pem $CA_KEY_SIZE

# Generate CA certificate (10 years)
openssl req -new -x509 -days $CERT_VALIDITY_DAYS -key ca/ca-key.pem \
    -out ca/ca-cert.pem \
    -subj "/C=US/ST=CA/L=San Francisco/O=Mister Smith Framework/OU=Security/CN=Mister Smith CA"

# Generate server private key
openssl genrsa -out server/server-key.pem 4096

# Generate server certificate signing request
openssl req -new -key server/server-key.pem -out server/server.csr \
    -subj "/C=US/ST=CA/L=San Francisco/O=Mister Smith Framework/OU=Services/CN=mister-smith.local"

# Create server certificate extensions
cat > server/server-ext.cnf << EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = mister-smith.local
DNS.2 = localhost
DNS.3 = *.mister-smith.local
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

# Sign server certificate (90 days)
openssl x509 -req -in server/server.csr -CA ca/ca-cert.pem -CAkey ca/ca-key.pem \
    -out server/server-cert.pem -days $SERVER_CERT_VALIDITY_DAYS \
    -extensions v3_req -extfile server/server-ext.cnf -CAcreateserial

# Generate client private key
openssl genrsa -out client/client-key.pem 4096

# Generate client certificate signing request
openssl req -new -key client/client-key.pem -out client/client.csr \
    -subj "/C=US/ST=CA/L=San Francisco/O=Mister Smith Framework/OU=Clients/CN=mister-smith-client"

# Create client certificate extensions
cat > client/client-ext.cnf << EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature
extendedKeyUsage = clientAuth
EOF

# Sign client certificate (365 days)
openssl x509 -req -in client/client.csr -CA ca/ca-cert.pem -CAkey ca/ca-key.pem \
    -out client/client-cert.pem -days $CLIENT_CERT_VALIDITY_DAYS \
    -extensions v3_req -extfile client/client-ext.cnf -CAcreateserial

# Set proper permissions
chmod 600 ca/ca-key.pem server/server-key.pem client/client-key.pem
chmod 644 ca/ca-cert.pem server/server-cert.pem client/client-cert.pem

echo "Certificates generated successfully in $CA_DIR"
```

**Certificate Rotation Script:**
```bash
#!/bin/bash
# rotate_certs.sh - Zero-downtime certificate rotation

set -euo pipefail

CA_DIR="/etc/mister-smith/certs"
BACKUP_DIR="/etc/mister-smith/certs/backup/$(date +%Y%m%d_%H%M%S)"

# Create backup
mkdir -p $BACKUP_DIR
cp -r $CA_DIR/* $BACKUP_DIR/

# Check certificate expiration (warn at 30 days)
check_expiration() {
    local cert_file=$1
    local threshold_days=30
    
    expiry_date=$(openssl x509 -in $cert_file -noout -enddate | cut -d= -f2)
    expiry_timestamp=$(date -d "$expiry_date" +%s)
    current_timestamp=$(date +%s)
    days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
    
    if [ $days_until_expiry -le $threshold_days ]; then
        echo "WARNING: Certificate $cert_file expires in $days_until_expiry days"
        return 1
    fi
    return 0
}

# Rotate server certificate
rotate_server_cert() {
    echo "Rotating server certificate..."
    
    # Generate new server key and certificate
    openssl genrsa -out $CA_DIR/server/server-key-new.pem 4096
    openssl req -new -key $CA_DIR/server/server-key-new.pem -out $CA_DIR/server/server-new.csr \
        -subj "/C=US/ST=CA/L=San Francisco/O=Mister Smith Framework/OU=Services/CN=mister-smith.local"
    
    openssl x509 -req -in $CA_DIR/server/server-new.csr -CA $CA_DIR/ca/ca-cert.pem \
        -CAkey $CA_DIR/ca/ca-key.pem -out $CA_DIR/server/server-cert-new.pem \
        -days 90 -extensions v3_req -extfile $CA_DIR/server/server-ext.cnf -CAcreateserial
    
    # Atomic replacement
    mv $CA_DIR/server/server-cert-new.pem $CA_DIR/server/server-cert.pem
    mv $CA_DIR/server/server-key-new.pem $CA_DIR/server/server-key.pem
    
    # Send SIGHUP to services for hot reload
    systemctl reload mister-smith-api
    systemctl reload nats-server
    
    echo "Server certificate rotated successfully"
}

# Check and rotate if needed
if ! check_expiration $CA_DIR/server/server-cert.pem; then
    rotate_server_cert
fi
```

### 1.2 Rustls Certificate Management Implementation

**Complete Certificate Manager:**
```rust
// certificate_manager.rs
use rustls::{Certificate, PrivateKey, ServerConfig, ClientConfig};
use rustls_pemfile::{certs, pkcs8_private_keys, rsa_private_keys};
use std::fs::File;
use std::io::BufReader;
use std::path::Path;
use std::sync::Arc;
use tokio::time::{Duration, interval};
use tracing::{info, warn, error};
use anyhow::{Result, Context};

#[derive(Clone)]
pub struct CertificateManager {
    ca_cert_path: String,
    server_cert_path: String,
    server_key_path: String,
    client_cert_path: String,
    client_key_path: String,
}

impl CertificateManager {
    pub fn new() -> Self {
        Self {
            ca_cert_path: "/etc/mister-smith/certs/ca/ca-cert.pem".to_string(),
            server_cert_path: "/etc/mister-smith/certs/server/server-cert.pem".to_string(),
            server_key_path: "/etc/mister-smith/certs/server/server-key.pem".to_string(),
            client_cert_path: "/etc/mister-smith/certs/client/client-cert.pem".to_string(),
            client_key_path: "/etc/mister-smith/certs/client/client-key.pem".to_string(),
        }
    }

    /// Load certificates from PEM files
    pub fn load_certificates(&self, path: &str) -> Result<Vec<Certificate>> {
        let file = File::open(path)
            .with_context(|| format!("Failed to open certificate file: {}", path))?;
        let mut reader = BufReader::new(file);
        
        let certs = certs(&mut reader)
            .with_context(|| "Failed to parse certificates")?
            .into_iter()
            .map(Certificate)
            .collect();

        if certs.is_empty() {
            anyhow::bail!("No certificates found in file: {}", path);
        }

        info!("Loaded {} certificates from {}", certs.len(), path);
        Ok(certs)
    }

    /// Load private key from PEM file
    pub fn load_private_key(&self, path: &str) -> Result<PrivateKey> {
        let file = File::open(path)
            .with_context(|| format!("Failed to open private key file: {}", path))?;
        let mut reader = BufReader::new(file);

        // Try PKCS8 format first
        if let Ok(mut keys) = pkcs8_private_keys(&mut reader) {
            if !keys.is_empty() {
                info!("Loaded PKCS8 private key from {}", path);
                return Ok(PrivateKey(keys.remove(0)));
            }
        }

        // Reset reader and try RSA format
        let file = File::open(path)?;
        let mut reader = BufReader::new(file);
        
        let mut keys = rsa_private_keys(&mut reader)
            .with_context(|| "Failed to parse RSA private key")?;

        if keys.is_empty() {
            anyhow::bail!("No private keys found in file: {}", path);
        }

        info!("Loaded RSA private key from {}", path);
        Ok(PrivateKey(keys.remove(0)))
    }

    /// Create TLS server configuration with mTLS
    pub fn create_server_config(&self) -> Result<Arc<ServerConfig>> {
        let certs = self.load_certificates(&self.server_cert_path)?;
        let key = self.load_private_key(&self.server_key_path)?;
        let ca_certs = self.load_certificates(&self.ca_cert_path)?;

        let mut root_store = rustls::RootCertStore::empty();
        for cert in ca_certs {
            root_store.add(&cert)
                .with_context(|| "Failed to add CA certificate to root store")?;
        }

        let client_cert_verifier = rustls::server::AllowAnyAuthenticatedClient::new(root_store);

        let config = ServerConfig::builder()
            .with_cipher_suites(&[
                rustls::cipher_suite::TLS13_AES_256_GCM_SHA384,
                rustls::cipher_suite::TLS13_CHACHA20_POLY1305_SHA256,
                rustls::cipher_suite::TLS13_AES_128_GCM_SHA256,
            ])
            .with_kx_groups(&[
                &rustls::kx_group::X25519,
                &rustls::kx_group::SECP384R1,
                &rustls::kx_group::SECP256R1,
            ])
            .with_protocol_versions(&[&rustls::version::TLS13])
            .with_context(|| "Failed to configure TLS parameters")?
            .with_client_cert_verifier(client_cert_verifier)
            .with_single_cert(certs, key)
            .with_context(|| "Failed to configure server certificate")?;

        info!("Created TLS server configuration with mTLS");
        Ok(Arc::new(config))
    }

    /// Create TLS client configuration
    pub fn create_client_config(&self) -> Result<Arc<ClientConfig>> {
        let certs = self.load_certificates(&self.client_cert_path)?;
        let key = self.load_private_key(&self.client_key_path)?;
        let ca_certs = self.load_certificates(&self.ca_cert_path)?;

        let mut root_store = rustls::RootCertStore::empty();
        for cert in ca_certs {
            root_store.add(&cert)
                .with_context(|| "Failed to add CA certificate to root store")?;
        }

        let config = ClientConfig::builder()
            .with_cipher_suites(&[
                rustls::cipher_suite::TLS13_AES_256_GCM_SHA384,
                rustls::cipher_suite::TLS13_CHACHA20_POLY1305_SHA256,
                rustls::cipher_suite::TLS13_AES_128_GCM_SHA256,
            ])
            .with_kx_groups(&[
                &rustls::kx_group::X25519,
                &rustls::kx_group::SECP384R1,
                &rustls::kx_group::SECP256R1,
            ])
            .with_protocol_versions(&[&rustls::version::TLS13])
            .with_context(|| "Failed to configure TLS parameters")?
            .with_root_certificates(root_store)
            .with_single_cert(certs, key)
            .with_context(|| "Failed to configure client certificate")?;

        info!("Created TLS client configuration");
        Ok(Arc::new(config))
    }

    /// Check certificate expiration
    pub fn check_certificate_expiration(&self, cert_path: &str) -> Result<Duration> {
        use x509_parser::prelude::*;
        
        let cert_data = std::fs::read(cert_path)
            .with_context(|| format!("Failed to read certificate: {}", cert_path))?;
            
        let pem = pem::parse(&cert_data)
            .with_context(|| "Failed to parse PEM certificate")?;
            
        let x509 = X509Certificate::from_der(&pem.contents)
            .with_context(|| "Failed to parse X509 certificate")?;

        let expiry_time = x509.1.validity().not_after.timestamp() as u64;
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();

        if expiry_time <= current_time {
            anyhow::bail!("Certificate has expired: {}", cert_path);
        }

        let remaining = Duration::from_secs(expiry_time - current_time);
        
        if remaining.as_secs() < 30 * 24 * 60 * 60 { // 30 days
            warn!("Certificate expires in {} days: {}", 
                remaining.as_secs() / (24 * 60 * 60), cert_path);
        }

        Ok(remaining)
    }

    /// Start certificate monitoring task
    pub async fn start_monitoring(&self) {
        let manager = self.clone();
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_hours(24));
            
            loop {
                interval.tick().await;
                
                // Check server certificate expiration
                if let Err(e) = manager.check_certificate_expiration(&manager.server_cert_path) {
                    error!("Server certificate check failed: {}", e);
                }
                
                // Check client certificate expiration
                if let Err(e) = manager.check_certificate_expiration(&manager.client_cert_path) {
                    error!("Client certificate check failed: {}", e);
                }
            }
        });
        
        info!("Started certificate monitoring task");
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_certificate_manager() {
        let temp_dir = TempDir::new().unwrap();
        // Add comprehensive tests for certificate operations
    }
}
```

## 2. JWT Authentication Implementation

### 2.1 JWT Service Implementation

**Complete JWT Authentication Service:**
```rust
// jwt_service.rs
use jwt_simple::prelude::*;
use serde::{Deserialize, Serialize};
use std::collections::HashSet;
use anyhow::{Result, Context};
use tracing::{info, warn, error};
use uuid::Uuid;

/// Standard security parameters - DO NOT MODIFY
const ACCESS_TOKEN_DURATION: u64 = 15 * 60; // 15 minutes
const REFRESH_TOKEN_DURATION: u64 = 7 * 24 * 60 * 60; // 7 days
const API_KEY_DURATION: u64 = 90 * 24 * 60 * 60; // 90 days

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Role {
    #[serde(rename = "readonly")]
    ReadOnly,
    #[serde(rename = "user")]
    User,
    #[serde(rename = "moderator")]
    Moderator,
    #[serde(rename = "admin")]
    Admin,
    #[serde(rename = "system")]
    System,
}

impl Role {
    pub fn permissions(&self) -> Vec<&'static str> {
        match self {
            Role::ReadOnly => vec!["read:own"],
            Role::User => vec!["read:own", "write:own"],
            Role::Moderator => vec!["read:own", "write:own", "read:team", "write:team"],
            Role::Admin => vec!["read:*", "write:*", "delete:*"],
            Role::System => vec!["read:*", "write:*", "delete:*", "admin:*"],
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserClaims {
    pub user_id: Uuid,
    pub tenant_id: Uuid,
    pub roles: Vec<Role>,
    pub permissions: Vec<String>,
    pub token_type: TokenType,
    pub session_id: Uuid,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TokenType {
    #[serde(rename = "access")]
    Access,
    #[serde(rename = "refresh")]
    Refresh,
    #[serde(rename = "api_key")]
    ApiKey,
}

pub struct JwtService {
    access_key: ES384KeyPair,
    refresh_key: ES384KeyPair,
    api_key: ES384KeyPair,
    issuer: String,
    audience: String,
}

impl JwtService {
    pub fn new() -> Result<Self> {
        // Generate separate key pairs for different token types
        let access_key = ES384KeyPair::generate();
        let refresh_key = ES384KeyPair::generate();
        let api_key = ES384KeyPair::generate();

        Ok(Self {
            access_key,
            refresh_key,
            api_key,
            issuer: "mister-smith-framework".to_string(),
            audience: "mister-smith-services".to_string(),
        })
    }

    /// Generate access token (15 minutes expiration)
    pub fn generate_access_token(&self, user_id: Uuid, tenant_id: Uuid, roles: Vec<Role>) -> Result<String> {
        let permissions = roles.iter()
            .flat_map(|role| role.permissions())
            .map(|p| p.to_string())
            .collect();

        let user_claims = UserClaims {
            user_id,
            tenant_id,
            roles,
            permissions,
            token_type: TokenType::Access,
            session_id: Uuid::new_v4(),
        };

        let claims = Claims::with_custom_claims(user_claims, Duration::from_secs(ACCESS_TOKEN_DURATION))
            .with_issuer(&self.issuer)
            .with_audience(&self.audience)
            .with_subject(&user_id.to_string());

        let token = self.access_key.sign(claims)
            .with_context(|| "Failed to sign access token")?;

        info!("Generated access token for user: {}", user_id);
        Ok(token)
    }

    /// Generate refresh token (7 days expiration)
    pub fn generate_refresh_token(&self, user_id: Uuid, tenant_id: Uuid, session_id: Uuid) -> Result<String> {
        let user_claims = UserClaims {
            user_id,
            tenant_id,
            roles: vec![], // Refresh tokens don't carry permissions
            permissions: vec![],
            token_type: TokenType::Refresh,
            session_id,
        };

        let claims = Claims::with_custom_claims(user_claims, Duration::from_secs(REFRESH_TOKEN_DURATION))
            .with_issuer(&self.issuer)
            .with_audience(&self.audience)
            .with_subject(&user_id.to_string());

        let token = self.refresh_key.sign(claims)
            .with_context(|| "Failed to sign refresh token")?;

        info!("Generated refresh token for user: {}", user_id);
        Ok(token)
    }

    /// Generate API key (90 days expiration)
    pub fn generate_api_key(&self, user_id: Uuid, tenant_id: Uuid, roles: Vec<Role>) -> Result<String> {
        let permissions = roles.iter()
            .flat_map(|role| role.permissions())
            .map(|p| p.to_string())
            .collect();

        let user_claims = UserClaims {
            user_id,
            tenant_id,
            roles,
            permissions,
            token_type: TokenType::ApiKey,
            session_id: Uuid::new_v4(),
        };

        let claims = Claims::with_custom_claims(user_claims, Duration::from_secs(API_KEY_DURATION))
            .with_issuer(&self.issuer)
            .with_audience(&self.audience)
            .with_subject(&user_id.to_string());

        let token = self.api_key.sign(claims)
            .with_context(|| "Failed to sign API key")?;

        info!("Generated API key for user: {}", user_id);
        Ok(token)
    }

    /// Verify access token
    pub fn verify_access_token(&self, token: &str) -> Result<Claims<UserClaims>> {
        let public_key = self.access_key.public_key();
        
        let mut options = VerificationOptions::default();
        options.allowed_issuers = Some(HashSet::from([self.issuer.clone()]));
        options.allowed_audiences = Some(HashSet::from([self.audience.clone()]));

        let claims = public_key.verify_token::<UserClaims>(token, Some(options))
            .with_context(|| "Failed to verify access token")?;

        // Verify token type
        if !matches!(claims.custom.token_type, TokenType::Access) {
            anyhow::bail!("Invalid token type for access token");
        }

        Ok(claims)
    }

    /// Verify refresh token
    pub fn verify_refresh_token(&self, token: &str) -> Result<Claims<UserClaims>> {
        let public_key = self.refresh_key.public_key();
        
        let mut options = VerificationOptions::default();
        options.allowed_issuers = Some(HashSet::from([self.issuer.clone()]));
        options.allowed_audiences = Some(HashSet::from([self.audience.clone()]));

        let claims = public_key.verify_token::<UserClaims>(token, Some(options))
            .with_context(|| "Failed to verify refresh token")?;

        // Verify token type
        if !matches!(claims.custom.token_type, TokenType::Refresh) {
            anyhow::bail!("Invalid token type for refresh token");
        }

        Ok(claims)
    }

    /// Verify API key
    pub fn verify_api_key(&self, token: &str) -> Result<Claims<UserClaims>> {
        let public_key = self.api_key.public_key();
        
        let mut options = VerificationOptions::default();
        options.allowed_issuers = Some(HashSet::from([self.issuer.clone()]));
        options.allowed_audiences = Some(HashSet::from([self.audience.clone()]));
        // API keys have longer validity, so allow more clock skew
        options.time_tolerance = Some(Duration::from_mins(30));

        let claims = public_key.verify_token::<UserClaims>(token, Some(options))
            .with_context(|| "Failed to verify API key")?;

        // Verify token type
        if !matches!(claims.custom.token_type, TokenType::ApiKey) {
            anyhow::bail!("Invalid token type for API key");
        }

        Ok(claims)
    }

    /// Check if user has permission for resource and action
    pub fn check_permission(&self, claims: &Claims<UserClaims>, resource: &str, action: &str) -> bool {
        let required_permission = format!("{}:{}", action, resource);
        let wildcard_permission = format!("{}:*", action);
        let super_wildcard = "admin:*";

        claims.custom.permissions.iter().any(|perm| {
            perm == &required_permission || 
            perm == &wildcard_permission || 
            perm == super_wildcard
        })
    }

    /// Refresh access token using refresh token
    pub fn refresh_access_token(&self, refresh_token: &str, new_roles: Option<Vec<Role>>) -> Result<String> {
        let refresh_claims = self.verify_refresh_token(refresh_token)?;
        
        // Use provided roles or fetch from user store
        let roles = new_roles.unwrap_or_else(|| vec![Role::User]);
        
        self.generate_access_token(
            refresh_claims.custom.user_id,
            refresh_claims.custom.tenant_id,
            roles
        )
    }
}

/// JWT Authentication Middleware
pub struct JwtMiddleware {
    jwt_service: JwtService,
}

impl JwtMiddleware {
    pub fn new(jwt_service: JwtService) -> Self {
        Self { jwt_service }
    }

    /// Extract and verify JWT from Authorization header
    pub fn authenticate_request(&self, auth_header: Option<&str>) -> Result<Claims<UserClaims>> {
        let auth_header = auth_header
            .ok_or_else(|| anyhow::anyhow!("Missing Authorization header"))?;

        let token = auth_header.strip_prefix("Bearer ")
            .ok_or_else(|| anyhow::anyhow!("Invalid Authorization header format"))?;

        // Try access token first
        if let Ok(claims) = self.jwt_service.verify_access_token(token) {
            return Ok(claims);
        }

        // Try API key if access token fails
        self.jwt_service.verify_api_key(token)
            .with_context(|| "Invalid or expired token")
    }

    /// Check authorization for specific resource and action
    pub fn authorize_request(&self, claims: &Claims<UserClaims>, resource: &str, action: &str) -> Result<()> {
        if !self.jwt_service.check_permission(claims, resource, action) {
            anyhow::bail!("Insufficient permissions for {} on {}", action, resource);
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_jwt_lifecycle() {
        let jwt_service = JwtService::new().unwrap();
        let user_id = Uuid::new_v4();
        let tenant_id = Uuid::new_v4();
        let roles = vec![Role::User];

        // Test access token generation and verification
        let access_token = jwt_service.generate_access_token(user_id, tenant_id, roles.clone()).unwrap();
        let claims = jwt_service.verify_access_token(&access_token).unwrap();
        assert_eq!(claims.custom.user_id, user_id);

        // Test permission checking
        assert!(jwt_service.check_permission(&claims, "own", "read"));
        assert!(!jwt_service.check_permission(&claims, "all", "delete"));
    }
}
```

## 3. Authorization Implementation

### 3.1 RBAC Policy Engine

**Complete RBAC Implementation:**
```rust
// rbac_engine.rs
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use uuid::Uuid;
use anyhow::{Result, Context};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Resource {
    pub id: String,
    pub resource_type: ResourceType,
    pub owner_id: Option<Uuid>,
    pub tenant_id: Uuid,
    pub team_id: Option<Uuid>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ResourceType {
    #[serde(rename = "user")]
    User,
    #[serde(rename = "project")]
    Project,
    #[serde(rename = "document")]
    Document,
    #[serde(rename = "system")]
    System,
    #[serde(rename = "configuration")]
    Configuration,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Action {
    #[serde(rename = "read")]
    Read,
    #[serde(rename = "write")]
    Write,
    #[serde(rename = "delete")]
    Delete,
    #[serde(rename = "admin")]
    Admin,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Permission {
    pub action: Action,
    pub resource_pattern: String,
    pub conditions: Vec<Condition>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Condition {
    #[serde(rename = "owner_only")]
    OwnerOnly,
    #[serde(rename = "same_tenant")]
    SameTenant,
    #[serde(rename = "same_team")]
    SameTeam,
    #[serde(rename = "business_hours")]
    BusinessHours,
}

pub struct RbacEngine {
    role_permissions: HashMap<Role, Vec<Permission>>,
}

impl RbacEngine {
    pub fn new() -> Self {
        let mut role_permissions = HashMap::new();
        
        // ReadOnly role permissions
        role_permissions.insert(Role::ReadOnly, vec![
            Permission {
                action: Action::Read,
                resource_pattern: "*".to_string(),
                conditions: vec![Condition::OwnerOnly, Condition::SameTenant],
            },
        ]);

        // User role permissions
        role_permissions.insert(Role::User, vec![
            Permission {
                action: Action::Read,
                resource_pattern: "*".to_string(),
                conditions: vec![Condition::OwnerOnly, Condition::SameTenant],
            },
            Permission {
                action: Action::Write,
                resource_pattern: "*".to_string(),
                conditions: vec![Condition::OwnerOnly, Condition::SameTenant],
            },
        ]);

        // Moderator role permissions
        role_permissions.insert(Role::Moderator, vec![
            Permission {
                action: Action::Read,
                resource_pattern: "*".to_string(),
                conditions: vec![Condition::SameTeam, Condition::SameTenant],
            },
            Permission {
                action: Action::Write,
                resource_pattern: "*".to_string(),
                conditions: vec![Condition::SameTeam, Condition::SameTenant],
            },
            Permission {
                action: Action::Delete,
                resource_pattern: "document".to_string(),
                conditions: vec![Condition::SameTeam, Condition::SameTenant],
            },
        ]);

        // Admin role permissions
        role_permissions.insert(Role::Admin, vec![
            Permission {
                action: Action::Read,
                resource_pattern: "*".to_string(),
                conditions: vec![Condition::SameTenant],
            },
            Permission {
                action: Action::Write,
                resource_pattern: "*".to_string(),
                conditions: vec![Condition::SameTenant],
            },
            Permission {
                action: Action::Delete,
                resource_pattern: "*".to_string(),
                conditions: vec![Condition::SameTenant],
            },
        ]);

        // System role permissions (no restrictions)
        role_permissions.insert(Role::System, vec![
            Permission {
                action: Action::Read,
                resource_pattern: "*".to_string(),
                conditions: vec![],
            },
            Permission {
                action: Action::Write,
                resource_pattern: "*".to_string(),
                conditions: vec![],
            },
            Permission {
                action: Action::Delete,
                resource_pattern: "*".to_string(),
                conditions: vec![],
            },
            Permission {
                action: Action::Admin,
                resource_pattern: "*".to_string(),
                conditions: vec![],
            },
        ]);

        Self { role_permissions }
    }

    /// Check if user has permission to perform action on resource
    pub fn check_permission(
        &self,
        user_claims: &UserClaims,
        resource: &Resource,
        action: &Action,
    ) -> Result<bool> {
        // Check each role the user has
        for role in &user_claims.roles {
            if let Some(permissions) = self.role_permissions.get(role) {
                for permission in permissions {
                    if self.permission_matches(permission, resource, action, user_claims)? {
                        return Ok(true);
                    }
                }
            }
        }
        
        Ok(false)
    }

    /// Check if permission matches the requested action and resource
    fn permission_matches(
        &self,
        permission: &Permission,
        resource: &Resource,
        action: &Action,
        user_claims: &UserClaims,
    ) -> Result<bool> {
        // Check action match
        if !self.action_matches(&permission.action, action) {
            return Ok(false);
        }

        // Check resource pattern match
        if !self.resource_pattern_matches(&permission.resource_pattern, resource) {
            return Ok(false);
        }

        // Check all conditions
        for condition in &permission.conditions {
            if !self.condition_matches(condition, resource, user_claims)? {
                return Ok(false);
            }
        }

        Ok(true)
    }

    /// Check if action matches (including hierarchical permissions)
    fn action_matches(&self, permission_action: &Action, requested_action: &Action) -> bool {
        match (permission_action, requested_action) {
            // Exact match
            (a, b) if a == b => true,
            // Admin action grants all permissions
            (Action::Admin, _) => true,
            // Write action grants read permission
            (Action::Write, Action::Read) => true,
            // Delete action grants read and write permissions
            (Action::Delete, Action::Read) | (Action::Delete, Action::Write) => true,
            _ => false,
        }
    }

    /// Check if resource pattern matches
    fn resource_pattern_matches(&self, pattern: &str, resource: &Resource) -> bool {
        if pattern == "*" {
            return true;
        }

        let resource_type_str = match resource.resource_type {
            ResourceType::User => "user",
            ResourceType::Project => "project",
            ResourceType::Document => "document",
            ResourceType::System => "system",
            ResourceType::Configuration => "configuration",
        };

        pattern == resource_type_str
    }

    /// Check if condition is satisfied
    fn condition_matches(
        &self,
        condition: &Condition,
        resource: &Resource,
        user_claims: &UserClaims,
    ) -> Result<bool> {
        match condition {
            Condition::OwnerOnly => {
                Ok(resource.owner_id == Some(user_claims.user_id))
            },
            Condition::SameTenant => {
                Ok(resource.tenant_id == user_claims.tenant_id)
            },
            Condition::SameTeam => {
                match (resource.team_id, &user_claims.roles) {
                    (Some(resource_team), _) => {
                        // For now, check if user has moderator role in same tenant
                        // In a real implementation, you'd check team membership
                        Ok(resource.tenant_id == user_claims.tenant_id)
                    },
                    (None, _) => Ok(true), // Resource not tied to team
                }
            },
            Condition::BusinessHours => {
                use chrono::{Local, Timelike};
                let now = Local::now();
                let hour = now.hour();
                Ok(hour >= 9 && hour <= 17) // 9 AM to 5 PM
            },
        }
    }

    /// Get effective permissions for user
    pub fn get_effective_permissions(&self, user_claims: &UserClaims) -> Vec<String> {
        let mut permissions = HashSet::new();
        
        for role in &user_claims.roles {
            if let Some(role_permissions) = self.role_permissions.get(role) {
                for permission in role_permissions {
                    let perm_str = format!("{}:{}", 
                        action_to_string(&permission.action),
                        permission.resource_pattern
                    );
                    permissions.insert(perm_str);
                }
            }
        }
        
        permissions.into_iter().collect()
    }
}

fn action_to_string(action: &Action) -> &'static str {
    match action {
        Action::Read => "read",
        Action::Write => "write", 
        Action::Delete => "delete",
        Action::Admin => "admin",
    }
}

/// Authorization middleware
pub struct AuthorizationMiddleware {
    rbac_engine: RbacEngine,
}

impl AuthorizationMiddleware {
    pub fn new() -> Self {
        Self {
            rbac_engine: RbacEngine::new(),
        }
    }

    /// Authorize request for specific resource and action
    pub fn authorize(
        &self,
        user_claims: &UserClaims,
        resource: &Resource,
        action: &Action,
    ) -> Result<()> {
        if self.rbac_engine.check_permission(user_claims, resource, action)? {
            Ok(())
        } else {
            anyhow::bail!(
                "Access denied: user {} lacks permission to {:?} resource {}",
                user_claims.user_id,
                action,
                resource.id
            )
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_rbac_permissions() {
        let rbac = RbacEngine::new();
        
        let user_claims = UserClaims {
            user_id: Uuid::new_v4(),
            tenant_id: Uuid::new_v4(),
            roles: vec![Role::User],
            permissions: vec![],
            token_type: TokenType::Access,
            session_id: Uuid::new_v4(),
        };

        let resource = Resource {
            id: "test-doc".to_string(),
            resource_type: ResourceType::Document,
            owner_id: Some(user_claims.user_id),
            tenant_id: user_claims.tenant_id,
            team_id: None,
        };

        // User should be able to read their own document
        assert!(rbac.check_permission(&user_claims, &resource, &Action::Read).unwrap());
        
        // User should be able to write their own document
        assert!(rbac.check_permission(&user_claims, &resource, &Action::Write).unwrap());
        
        // User should NOT be able to delete their own document (requires moderator+)
        assert!(!rbac.check_permission(&user_claims, &resource, &Action::Delete).unwrap());
    }
}
```

---

This document provides foundational security patterns for agent implementation. For complete system architecture and advanced patterns, refer to the canonical tech-framework.md.